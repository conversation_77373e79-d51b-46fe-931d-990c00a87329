//
//  GameScene.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import GameplayKit
import UIKit

class GameScene: SKScene {

    // MARK: - Properties
    private var puzzlePieces: [PuzzlePiece] = []
    private var selectedPiece: PuzzlePiece?
    private var puzzleBoard: SKNode!
    private var piecesContainer: SKNode!
    private var backButton: SKNode!
    private var timerLabel: SKLabelNode!
    private var movesLabel: SKLabelNode!
    private var referenceImageNode: SKSpriteNode!

    // Puzzle configuration
    private let pieceCount: Int
    private var pieceSize: CGFloat = 80
    private var boardSize: CGFloat = 300
    private var puzzleImageSize: CGSize = CGSize(width: 300, height: 300)
    private var gridCols: Int = 2
    private var gridRows: Int = 2

    // Game state
    private var startTime: Date?
    private var moveCount: Int = 0

    // MARK: - Initialization
    init(pieceCount: Int = 10) {
        self.pieceCount = pieceCount
        super.init(size: .zero)
    }

    required init?(coder aDecoder: NSCoder) {
        self.pieceCount = 10 // Default piece count
        super.init(coder: aDecoder)
    }

    // MARK: - Scene Setup
    override func didMove(to view: SKView) {
        print("🎮 GameScene didMove to view")
        print("📏 Scene size: \(size)")
        print("🖼️ View bounds: \(view.bounds)")

        // Enable user interaction
        isUserInteractionEnabled = true
        print("✅ User interaction enabled: \(isUserInteractionEnabled)")

        setupScene()
        createPuzzle()
        startTimer()
    }

    private func setupScene() {
        backgroundColor = SKColor(red: 0.15, green: 0.15, blue: 0.3, alpha: 1.0)

        // Set anchor point to bottom-left for easier positioning
        anchorPoint = CGPoint(x: 0.5, y: 0.5)

        // Calculate responsive sizes
        let screenWidth = size.width
        let screenHeight = size.height

        // Calculate puzzle board size based on screen
        boardSize = min(screenWidth * 0.7, screenHeight * 0.4)
        puzzleImageSize = CGSize(width: boardSize, height: boardSize)

        // Calculate piece size based on piece count (approximate)
        let approximateGridSize = sqrt(Double(pieceCount))
        pieceSize = boardSize / CGFloat(approximateGridSize) * 0.8

        print("🎮 Scene size: \(screenWidth) x \(screenHeight)")
        print("🧩 Piece count: \(pieceCount)")
        print("🧩 Piece size: \(pieceSize)")
        print("🖼️ Board size: \(boardSize)")

        // Create puzzle board (upper area)
        puzzleBoard = SKNode()
        puzzleBoard.position = CGPoint(x: 0, y: screenHeight * 0.2)
        addChild(puzzleBoard)

        // Add board background with grid
        let boardBg = SKShapeNode(rectOf: CGSize(width: boardSize, height: boardSize))
        boardBg.fillColor = UIColor.white.withAlphaComponent(0.1)
        boardBg.strokeColor = .white
        boardBg.lineWidth = 3
        puzzleBoard.addChild(boardBg)

        // Grid lines will be added after puzzle creation when we know the dimensions

        // Add reference image
        addReferenceImage()

        // Create pieces container (lower area)
        piecesContainer = SKNode()
        piecesContainer.position = CGPoint(x: 0, y: -screenHeight * 0.2)
        addChild(piecesContainer)

        // Add back button
        createBackButton()

        // Add game info UI
        createGameInfoUI()

        // Add title
        let title = SKLabelNode(text: "Nature Puzzle")
        title.fontName = "Helvetica-Bold"
        title.fontSize = 24
        title.fontColor = .white
        title.position = CGPoint(x: 0, y: screenHeight * 0.4)
        addChild(title)

        // Add difficulty indicator
        let difficultyText = "\(pieceCount) Pieces"
        let difficulty = SKLabelNode(text: difficultyText)
        difficulty.fontName = "Helvetica"
        difficulty.fontSize = 16
        difficulty.fontColor = .lightGray
        difficulty.position = CGPoint(x: 0, y: screenHeight * 0.35)
        addChild(difficulty)

        // Add instruction
        let instruction = SKLabelNode(text: "Drag pieces to complete the nature scene!")
        instruction.fontName = "Helvetica"
        instruction.fontSize = 14
        instruction.fontColor = .lightGray
        instruction.position = CGPoint(x: 0, y: -screenHeight * 0.45)
        addChild(instruction)

        print("📍 Board at: \(puzzleBoard.position)")
        print("📍 Pieces at: \(piecesContainer.position)")
    }

    private func createBackButton() {
        backButton = SKNode()
        backButton.position = CGPoint(x: -size.width * 0.4, y: size.height * 0.4)
        backButton.name = "backButton"

        // Button background
        let buttonBg = SKShapeNode(rectOf: CGSize(width: 80, height: 40), cornerRadius: 10)
        buttonBg.fillColor = UIColor.red.withAlphaComponent(0.8)
        buttonBg.strokeColor = .red
        buttonBg.lineWidth = 2
        backButton.addChild(buttonBg)

        // Button text
        let buttonText = SKLabelNode(text: "Back")
        buttonText.fontName = "Helvetica-Bold"
        buttonText.fontSize = 16
        buttonText.fontColor = .white
        buttonText.verticalAlignmentMode = .center
        backButton.addChild(buttonText)

        addChild(backButton)
    }

    private func createGameInfoUI() {
        // Timer label
        timerLabel = SKLabelNode(text: "Time: 00:00")
        timerLabel.fontName = "Helvetica"
        timerLabel.fontSize = 16
        timerLabel.fontColor = .white
        timerLabel.position = CGPoint(x: size.width * 0.3, y: size.height * 0.4)
        addChild(timerLabel)

        // Moves label
        movesLabel = SKLabelNode(text: "Moves: 0")
        movesLabel.fontName = "Helvetica"
        movesLabel.fontSize = 16
        movesLabel.fontColor = .white
        movesLabel.position = CGPoint(x: size.width * 0.3, y: size.height * 0.35)
        addChild(movesLabel)
    }

    private func startTimer() {
        startTime = Date()

        // Update timer every second
        let updateTimer = SKAction.run { [weak self] in
            self?.updateTimer()
        }
        let wait = SKAction.wait(forDuration: 1.0)
        let sequence = SKAction.sequence([updateTimer, wait])
        let repeatAction = SKAction.repeatForever(sequence)

        run(repeatAction, withKey: "timer")
    }

    private func updateTimer() {
        guard let startTime = startTime else { return }

        let elapsed = Date().timeIntervalSince(startTime)
        let minutes = Int(elapsed) / 60
        let seconds = Int(elapsed) % 60

        timerLabel.text = String(format: "Time: %02d:%02d", minutes, seconds)
    }

    private func incrementMoveCount() {
        moveCount += 1
        movesLabel.text = "Moves: \(moveCount)"
    }

    private func addGridLines() {
        // Add grid lines to show where each piece should go
        let gridLineColor = UIColor.white.withAlphaComponent(0.5)

        // Vertical lines
        for i in 1..<gridCols {
            let x = -boardSize/2 + (boardSize/CGFloat(gridCols)) * CGFloat(i)
            let line = SKShapeNode()
            let path = CGMutablePath()
            path.move(to: CGPoint(x: x, y: -boardSize/2))
            path.addLine(to: CGPoint(x: x, y: boardSize/2))
            line.path = path
            line.strokeColor = gridLineColor
            line.lineWidth = 1
            puzzleBoard.addChild(line)
        }

        // Horizontal lines
        for i in 1..<gridRows {
            let y = -boardSize/2 + (boardSize/CGFloat(gridRows)) * CGFloat(i)
            let line = SKShapeNode()
            let path = CGMutablePath()
            path.move(to: CGPoint(x: -boardSize/2, y: y))
            path.addLine(to: CGPoint(x: boardSize/2, y: y))
            line.path = path
            line.strokeColor = gridLineColor
            line.lineWidth = 1
            puzzleBoard.addChild(line)
        }

        // Add position numbers in each grid cell
        for row in 0..<gridRows {
            for col in 0..<gridCols {
                let pieceNumber = row * gridCols + col + 1
                let cellSize = boardSize / CGFloat(max(gridRows, gridCols))

                let x = -boardSize/2 + (boardSize/CGFloat(gridCols)) * CGFloat(col) + (boardSize/CGFloat(gridCols))/2
                let y = boardSize/2 - (boardSize/CGFloat(gridRows)) * CGFloat(row) - (boardSize/CGFloat(gridRows))/2

                let numberLabel = SKLabelNode(text: "\(pieceNumber)")
                numberLabel.fontName = "Helvetica-Bold"
                numberLabel.fontSize = cellSize * 0.3
                numberLabel.fontColor = UIColor.white.withAlphaComponent(0.3)
                numberLabel.position = CGPoint(x: x, y: y - numberLabel.fontSize/3)
                numberLabel.verticalAlignmentMode = .center
                puzzleBoard.addChild(numberLabel)
            }
        }
    }

    private func addReferenceImage() {
        // Create a small reference image showing the complete puzzle
        let referenceSize = min(size.width * 0.25, 120)
        let completeTexture = createCompleteReferenceTexture(size: CGSize(width: referenceSize, height: referenceSize))

        referenceImageNode = SKSpriteNode(texture: completeTexture)
        referenceImageNode.size = CGSize(width: referenceSize, height: referenceSize)
        referenceImageNode.position = CGPoint(x: size.width * 0.35, y: size.height * 0.25)

        // Add border
        let border = SKShapeNode(rectOf: CGSize(width: referenceSize + 4, height: referenceSize + 4))
        border.fillColor = .clear
        border.strokeColor = .white
        border.lineWidth = 2
        referenceImageNode.addChild(border)

        // Add label
        let label = SKLabelNode(text: "Goal")
        label.fontName = "Helvetica-Bold"
        label.fontSize = 14
        label.fontColor = .white
        label.position = CGPoint(x: 0, y: -referenceSize/2 - 20)
        referenceImageNode.addChild(label)

        addChild(referenceImageNode)
    }


    // MARK: - Puzzle Creation
    private func createPuzzle() {
        // Create image-based jigsaw puzzle
        createJigsawPuzzle()
        scramblePieces()
    }

    private func createJigsawPuzzle() {
        print("🧩 Creating simple puzzle with \(pieceCount) pieces")

        // Generate simple rectangular puzzle pieces
        let puzzleGenerator = ImagePuzzleGenerator(pieceCount: pieceCount, targetSize: puzzleImageSize)
        let puzzleData = puzzleGenerator.generatePuzzle()

        // Store the pieces
        puzzlePieces = puzzleData.pieces

        // Calculate grid dimensions from the actual pieces created
        if !puzzlePieces.isEmpty {
            gridCols = puzzlePieces.map { $0.correctCol }.max()! + 1
            gridRows = puzzlePieces.map { $0.correctRow }.max()! + 1
            print("📐 Grid dimensions: \(gridRows) rows x \(gridCols) cols")
        }

        // Add pieces to container
        for piece in puzzlePieces {
            piecesContainer.addChild(piece)
            print("✅ Created puzzle piece \(piece.pieceID)")
        }

        // Update reference image
        updateReferenceImage(with: puzzleData.referenceImage)

        // Now that we have grid dimensions, add grid lines
        addGridLines()

        print("📦 Total puzzle pieces created: \(puzzlePieces.count)")
        print("📍 Pieces container position: \(piecesContainer.position)")
    }

    private func updateReferenceImage(with image: UIImage) {
        // Update the reference image with the actual puzzle image
        let texture = SKTexture(image: image)
        referenceImageNode.texture = texture
    }

    private func createTextureFromColor(_ color: UIColor, size: CGSize, pieceNumber: Int) -> SKTexture {
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            color.setFill()
            context.fill(CGRect(origin: .zero, size: size))

            // Add some pattern to make pieces distinguishable
            UIColor.white.withAlphaComponent(0.3).setStroke()
            let path = UIBezierPath()
            path.move(to: CGPoint(x: 0, y: size.height/2))
            path.addLine(to: CGPoint(x: size.width, y: size.height/2))
            path.move(to: CGPoint(x: size.width/2, y: 0))
            path.addLine(to: CGPoint(x: size.width/2, y: size.height))
            path.lineWidth = 2
            path.stroke()

            // Add piece number
            UIColor.white.setFill()
            let fontSize = min(size.width, size.height) * 0.3
            let font = UIFont.boldSystemFont(ofSize: fontSize)
            let text = "\(pieceNumber)"
            let textSize = text.size(withAttributes: [.font: font])
            let textRect = CGRect(
                x: (size.width - textSize.width) / 2,
                y: (size.height - textSize.height) / 2,
                width: textSize.width,
                height: textSize.height
            )
            text.draw(in: textRect, withAttributes: [
                .font: font,
                .foregroundColor: UIColor.white
            ])
        }
        return SKTexture(image: image)
    }

    private func createCompleteReferenceTexture(size: CGSize) -> SKTexture {
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            // Create the complete puzzle image showing all pieces in order
            let baseColors: [UIColor] = [.red, .blue, .green, .yellow, .orange, .purple, .cyan, .magenta, .brown, .systemPink, .systemIndigo, .systemTeal, .systemMint, .systemYellow, .systemOrange, .systemRed]

            let cellWidth = size.width / CGFloat(gridCols)
            let cellHeight = size.height / CGFloat(gridRows)

            for row in 0..<gridRows {
                for col in 0..<gridCols {
                    let pieceIndex = row * gridCols + col
                    let color = baseColors[pieceIndex % baseColors.count]

                    // Draw each piece
                    let rect = CGRect(
                        x: CGFloat(col) * cellWidth,
                        y: CGFloat(row) * cellHeight,
                        width: cellWidth,
                        height: cellHeight
                    )

                    color.setFill()
                    context.fill(rect)

                    // Add border
                    UIColor.white.setStroke()
                    let borderPath = UIBezierPath(rect: rect)
                    borderPath.lineWidth = 1
                    borderPath.stroke()

                    // Add piece number
                    let pieceNumber = pieceIndex + 1
                    let fontSize = min(cellWidth, cellHeight) * 0.4
                    let font = UIFont.boldSystemFont(ofSize: fontSize)
                    let text = "\(pieceNumber)"
                    let textSize = text.size(withAttributes: [.font: font])
                    let textRect = CGRect(
                        x: rect.midX - textSize.width / 2,
                        y: rect.midY - textSize.height / 2,
                        width: textSize.width,
                        height: textSize.height
                    )
                    text.draw(in: textRect, withAttributes: [
                        .font: font,
                        .foregroundColor: UIColor.white
                    ])
                }
            }
        }
        return SKTexture(image: image)
    }

    private func scramblePieces() {
        print("🔄 Randomly placing \(puzzlePieces.count) pieces at the bottom")

        // Define the bottom area where pieces will be placed
        let bottomAreaHeight: CGFloat = size.height * 0.3
        let bottomArea = CGRect(
            x: -size.width/2 + 50,
            y: -size.height/2 + 50,
            width: size.width - 100,
            height: bottomAreaHeight
        )

        for (index, piece) in puzzlePieces.enumerated() {
            var randomPosition: CGPoint
            var attempts = 0
            let maxAttempts = 50

            repeat {
                randomPosition = CGPoint(
                    x: CGFloat.random(in: bottomArea.minX...bottomArea.maxX),
                    y: CGFloat.random(in: bottomArea.minY...bottomArea.maxY)
                )
                attempts += 1
            } while isPositionTooClose(randomPosition, to: puzzlePieces.prefix(index).map { $0 }) && attempts < maxAttempts

            // Add slight random rotation for more natural look
            let randomRotation = CGFloat.random(in: -0.2...0.2) // Small rotation in radians
            piece.zRotation = randomRotation

            piece.position = randomPosition
            piece.setOriginalPosition(randomPosition)
            piece.zPosition = CGFloat(index)

            print("🧩 Piece \(index) placed at: \(piece.position) with rotation: \(randomRotation)")
        }
    }

    private func isPositionTooClose(_ position: CGPoint, to pieces: [PuzzlePiece]) -> Bool {
        let minDistance: CGFloat = pieceSize * 0.8

        for piece in pieces {
            let distance = sqrt(pow(piece.position.x - position.x, 2) + pow(piece.position.y - position.y, 2))
            if distance < minDistance {
                return true
            }
        }
        return false
    }

    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let location = touch.location(in: self)

        print("👆 Touch began at: \(location)")

        // Check if back button was touched
        if backButton.contains(location) {
            goBackToMenu()
            return
        }

        // Check all puzzle pieces for touch
        var closestPiece: PuzzlePiece?
        var closestDistance: CGFloat = CGFloat.greatestFiniteMagnitude

        for piece in puzzlePieces {
            // Skip pieces that are already correctly placed
            if piece.isCorrectlyPlaced {
                continue
            }

            // Get the piece's position in scene coordinates
            let pieceScenePosition = convert(piece.position, from: piece.parent!)
            let distance = sqrt(pow(pieceScenePosition.x - location.x, 2) +
                              pow(pieceScenePosition.y - location.y, 2))

            print("🧩 Piece \(piece.pieceID) at scene pos: \(pieceScenePosition), distance: \(distance)")

            // Check if touch is within piece bounds with some tolerance
            if distance < pieceSize/2 + 10 && distance < closestDistance {
                closestPiece = piece
                closestDistance = distance
            }
        }

        if let piece = closestPiece {
            selectedPiece = piece
            piece.highlight()
            piece.zPosition = 1000
            incrementMoveCount()
            print("🎯 Selected piece: \(piece.pieceID)")
        } else {
            print("❌ No piece selected")
        }
    }

    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first,
              let piece = selectedPiece else { return }

        let location = touch.location(in: self)

        // Convert the touch location to the piece's parent coordinate system
        if let parent = piece.parent {
            let locationInParent = convert(location, to: parent)
            piece.position = locationInParent
            print("🚚 Moving piece \(piece.pieceID) to parent coords: \(locationInParent) (scene: \(location))")
        } else {
            piece.position = location
            print("🚚 Moving piece \(piece.pieceID) to scene coords: \(location)")
        }
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let piece = selectedPiece else { return }

        print("🏁 Touch ended with piece: \(piece.pieceID)")

        piece.unhighlight()
        piece.zPosition = CGFloat(piece.pieceID)

        // Convert board position to piece's parent coordinate system for accurate checking
        let boardPositionInPieceParent = convert(puzzleBoard.position, to: piece.parent!)

        // Check if piece is in correct position
        if piece.checkIfInCorrectPosition(boardPosition: boardPositionInPieceParent, boardSize: puzzleImageSize, gridCols: gridCols, gridRows: gridRows) {
            print("✅ Piece \(piece.pieceID) is in correct position!")

            // Reset rotation when piece is correctly placed
            piece.zRotation = 0

            piece.snapToCorrectPosition(boardPosition: boardPositionInPieceParent, boardSize: puzzleImageSize, gridCols: gridCols, gridRows: gridRows) { [weak self] in
                // Check completion after the piece state is fully updated
                self?.checkPuzzleCompletion()
            }
        } else {
            print("❌ Piece \(piece.pieceID) not in correct position, returning to original")
            piece.returnToOriginalPosition()
        }

        selectedPiece = nil
    }

    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        selectedPiece?.unhighlight()
        selectedPiece?.returnToOriginalPosition()
        selectedPiece = nil
    }

    // MARK: - Game Logic
    private func checkPuzzleCompletion() {
        let completedPieces = puzzlePieces.filter { $0.isCorrectlyPlaced }

        print("🔍 Checking puzzle completion:")
        print("   Total pieces: \(puzzlePieces.count)")
        print("   Completed pieces: \(completedPieces.count)")

        for piece in puzzlePieces {
            print("   Piece \(piece.pieceID): \(piece.isCorrectlyPlaced ? "✅" : "❌")")
        }

        if completedPieces.count == puzzlePieces.count {
            print("🎉 PUZZLE COMPLETED!")
            puzzleCompleted()
        } else {
            print("⏳ Puzzle not yet complete (\(completedPieces.count)/\(puzzlePieces.count))")
        }
    }

    private func puzzleCompleted() {
        // Stop timer
        removeAction(forKey: "timer")

        // Calculate final time
        let finalTime = startTime != nil ? Date().timeIntervalSince(startTime!) : 0
        let minutes = Int(finalTime) / 60
        let seconds = Int(finalTime) % 60

        // Haptic feedback for completion
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)

        // Visual celebration
        let celebrationLabel = SKLabelNode(text: "Congratulations! 🎉")
        celebrationLabel.fontName = "Helvetica-Bold"
        celebrationLabel.fontSize = 28
        celebrationLabel.fontColor = .yellow
        celebrationLabel.position = CGPoint(x: 0, y: 50)
        addChild(celebrationLabel)

        // Show completion stats
        let statsLabel = SKLabelNode(text: String(format: "Time: %02d:%02d | Moves: %d", minutes, seconds, moveCount))
        statsLabel.fontName = "Helvetica"
        statsLabel.fontSize = 18
        statsLabel.fontColor = .white
        statsLabel.position = CGPoint(x: 0, y: 10)
        addChild(statsLabel)

        // Animate celebration
        let scaleUp = SKAction.scale(to: 1.2, duration: 0.3)
        let scaleDown = SKAction.scale(to: 1.0, duration: 0.3)
        let sequence = SKAction.sequence([scaleUp, scaleDown])
        celebrationLabel.run(SKAction.repeat(sequence, count: 3))

        // Remove after animation and return to menu
        let wait = SKAction.wait(forDuration: 3.0)
        let fadeOut = SKAction.fadeOut(withDuration: 0.5)
        let remove = SKAction.removeFromParent()
        let returnToMenu = SKAction.run { [weak self] in
            self?.goBackToMenu()
        }
        celebrationLabel.run(SKAction.sequence([wait, fadeOut, remove, returnToMenu]))
        statsLabel.run(SKAction.sequence([wait, fadeOut, remove]))
    }

    private func goBackToMenu() {
        // Stop timer
        removeAction(forKey: "timer")

        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        // Create menu scene
        let menuScene = MenuScene()
        menuScene.size = size
        menuScene.scaleMode = scaleMode

        // Transition to menu scene
        let transition = SKTransition.fade(withDuration: 0.5)
        view?.presentScene(menuScene, transition: transition)
    }
}
