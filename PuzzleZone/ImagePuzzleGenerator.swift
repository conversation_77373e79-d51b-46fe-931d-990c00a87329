//
//  ImagePuzzleGenerator.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import UIKit

class ImagePuzzleGenerator {

    // MARK: - Properties
    private let pieceCount: Int
    private let targetSize: CGSize
    private let gridCols: Int
    private let gridRows: Int

    // MARK: - Initialization
    init(pieceCount: Int, targetSize: CGSize) {
        self.pieceCount = pieceCount
        self.targetSize = targetSize

        // Calculate grid dimensions for simple rectangular pieces
        let sqrt = Int(sqrt(Double(pieceCount)))
        self.gridCols = sqrt
        self.gridRows = (pieceCount + sqrt - 1) / sqrt // Ceiling division
    }

    // MARK: - Public Methods
    func generatePuzzle() -> (pieces: [PuzzlePiece], referenceImage: UIImage) {
        // Create a beautiful nature image
        let baseImage = createNatureImage()

        var puzzlePieces: [PuzzlePiece] = []
        let pieceWidth = targetSize.width / CGFloat(gridCols)
        let pieceHeight = targetSize.height / CGFloat(gridRows)

        var pieceID = 0
        for row in 0..<gridRows {
            for col in 0..<gridCols {
                if pieceID >= pieceCount { break }

                // Create texture from image portion
                let texture = createTextureFromImagePortion(
                    image: baseImage,
                    row: row,
                    col: col,
                    pieceWidth: pieceWidth,
                    pieceHeight: pieceHeight
                )

                // Create puzzle piece
                let piece = PuzzlePiece(
                    texture: texture,
                    row: row,
                    col: col,
                    pieceID: pieceID
                )

                piece.size = CGSize(width: pieceWidth, height: pieceHeight)
                puzzlePieces.append(piece)
                pieceID += 1
            }
        }

        return (pieces: puzzlePieces, referenceImage: baseImage)
    }

    // MARK: - Private Methods
    private func createNatureImage() -> UIImage {
        // Try to load the nature photo from assets first
        if let natureImage = UIImage(named: "nature_photo") {
            return natureImage
        }

        // If no asset found, create a beautiful nature scene
        let renderer = UIGraphicsImageRenderer(size: targetSize)
        let image = renderer.image { context in
            // Sky gradient (blue to light blue)
            let skyColors = [
                UIColor(red: 0.5, green: 0.8, blue: 1.0, alpha: 1.0).cgColor,
                UIColor(red: 0.8, green: 0.9, blue: 1.0, alpha: 1.0).cgColor
            ]
            let skyGradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: skyColors as CFArray, locations: nil)!
            context.cgContext.drawLinearGradient(skyGradient, start: .zero, end: CGPoint(x: 0, y: targetSize.height * 0.6), options: [])

            // Ground gradient (green to brown)
            let groundColors = [
                UIColor(red: 0.2, green: 0.6, blue: 0.2, alpha: 1.0).cgColor,
                UIColor(red: 0.4, green: 0.3, blue: 0.1, alpha: 1.0).cgColor
            ]
            let groundGradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: groundColors as CFArray, locations: nil)!
            context.cgContext.drawLinearGradient(groundGradient, start: CGPoint(x: 0, y: targetSize.height * 0.6), end: CGPoint(x: 0, y: targetSize.height), options: [])

            // Add mountains
            addMountains(to: context.cgContext, size: targetSize)

            // Add trees
            addTrees(to: context.cgContext, size: targetSize)

            // Add sun
            addSun(to: context.cgContext, size: targetSize)

            // Add clouds
            addClouds(to: context.cgContext, size: targetSize)
        }
        return image
    }

    private func addMountains(to context: CGContext, size: CGSize) {
        context.saveGState()

        // Mountain silhouette
        context.setFillColor(UIColor(red: 0.3, green: 0.3, blue: 0.5, alpha: 1.0).cgColor)

        let mountainPath = UIBezierPath()
        mountainPath.move(to: CGPoint(x: 0, y: size.height * 0.6))
        mountainPath.addLine(to: CGPoint(x: size.width * 0.2, y: size.height * 0.4))
        mountainPath.addLine(to: CGPoint(x: size.width * 0.4, y: size.height * 0.5))
        mountainPath.addLine(to: CGPoint(x: size.width * 0.6, y: size.height * 0.3))
        mountainPath.addLine(to: CGPoint(x: size.width * 0.8, y: size.height * 0.45))
        mountainPath.addLine(to: CGPoint(x: size.width, y: size.height * 0.4))
        mountainPath.addLine(to: CGPoint(x: size.width, y: size.height * 0.6))
        mountainPath.close()

        context.addPath(mountainPath.cgPath)
        context.fillPath()

        context.restoreGState()
    }

    private func addTrees(to context: CGContext, size: CGSize) {
        context.saveGState()

        // Tree positions
        let treePositions = [
            CGPoint(x: size.width * 0.15, y: size.height * 0.6),
            CGPoint(x: size.width * 0.35, y: size.height * 0.6),
            CGPoint(x: size.width * 0.65, y: size.height * 0.6),
            CGPoint(x: size.width * 0.85, y: size.height * 0.6)
        ]

        for position in treePositions {
            // Tree trunk
            context.setFillColor(UIColor(red: 0.4, green: 0.2, blue: 0.1, alpha: 1.0).cgColor)
            let trunkRect = CGRect(x: position.x - 5, y: position.y, width: 10, height: size.height * 0.15)
            context.fill(trunkRect)

            // Tree crown
            context.setFillColor(UIColor(red: 0.1, green: 0.5, blue: 0.1, alpha: 1.0).cgColor)
            let crownRadius: CGFloat = 25
            let crownRect = CGRect(x: position.x - crownRadius, y: position.y - crownRadius, width: crownRadius * 2, height: crownRadius * 2)
            context.fillEllipse(in: crownRect)
        }

        context.restoreGState()
    }

    private func addSun(to context: CGContext, size: CGSize) {
        context.saveGState()

        let sunCenter = CGPoint(x: size.width * 0.8, y: size.height * 0.2)
        let sunRadius: CGFloat = 30

        // Sun
        context.setFillColor(UIColor.yellow.cgColor)
        let sunRect = CGRect(x: sunCenter.x - sunRadius, y: sunCenter.y - sunRadius, width: sunRadius * 2, height: sunRadius * 2)
        context.fillEllipse(in: sunRect)

        // Sun rays
        context.setStrokeColor(UIColor.yellow.cgColor)
        context.setLineWidth(3)

        for i in 0..<8 {
            let angle = CGFloat(i) * .pi / 4
            let startX = sunCenter.x + cos(angle) * (sunRadius + 5)
            let startY = sunCenter.y + sin(angle) * (sunRadius + 5)
            let endX = sunCenter.x + cos(angle) * (sunRadius + 15)
            let endY = sunCenter.y + sin(angle) * (sunRadius + 15)

            context.move(to: CGPoint(x: startX, y: startY))
            context.addLine(to: CGPoint(x: endX, y: endY))
            context.strokePath()
        }

        context.restoreGState()
    }

    private func addClouds(to context: CGContext, size: CGSize) {
        context.saveGState()

        context.setFillColor(UIColor.white.cgColor)

        // Cloud 1
        let cloud1Center = CGPoint(x: size.width * 0.3, y: size.height * 0.25)
        addCloud(to: context, center: cloud1Center, size: 20)

        // Cloud 2
        let cloud2Center = CGPoint(x: size.width * 0.7, y: size.height * 0.15)
        addCloud(to: context, center: cloud2Center, size: 15)

        context.restoreGState()
    }

    private func addCloud(to context: CGContext, center: CGPoint, size: CGFloat) {
        // Create cloud with multiple circles
        let circles = [
            CGRect(x: center.x - size, y: center.y - size/2, width: size * 2, height: size),
            CGRect(x: center.x - size * 0.7, y: center.y - size * 0.8, width: size * 1.4, height: size * 0.8),
            CGRect(x: center.x - size * 0.3, y: center.y - size * 0.9, width: size * 1.2, height: size * 0.8),
            CGRect(x: center.x + size * 0.2, y: center.y - size * 0.7, width: size * 1.0, height: size * 0.7)
        ]

        for circle in circles {
            context.fillEllipse(in: circle)
        }
    }

    private func createTextureFromImagePortion(image: UIImage, row: Int, col: Int, pieceWidth: CGFloat, pieceHeight: CGFloat) -> SKTexture {
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: pieceWidth, height: pieceHeight))

        let textureImage = renderer.image { context in
            // Calculate the source rectangle in the original image
            let sourceRect = CGRect(
                x: CGFloat(col) * pieceWidth,
                y: CGFloat(row) * pieceHeight,
                width: pieceWidth,
                height: pieceHeight
            )

            // Draw the image portion
            let drawRect = CGRect(x: -sourceRect.minX, y: -sourceRect.minY, width: targetSize.width, height: targetSize.height)
            image.draw(in: drawRect)

            // Add border to piece
            context.cgContext.setStrokeColor(UIColor.black.withAlphaComponent(0.5).cgColor)
            context.cgContext.setLineWidth(2)
            context.cgContext.stroke(CGRect(x: 0, y: 0, width: pieceWidth, height: pieceHeight))
        }

        return SKTexture(image: textureImage)
    }
}
