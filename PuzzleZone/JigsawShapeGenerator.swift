//
//  JigsawShapeGenerator.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import UIKit

struct JigsawPieceInfo {
    let row: Int
    let col: Int
    let pieceID: Int
    let shape: UIBezierPath
    let imageRect: CGRect
    let hasTopTab: Bool
    let hasRightTab: Bool
    let hasBottomTab: Bool
    let hasLeftTab: Bool
}

class JigsawShapeGenerator {

    // MARK: - Properties
    private let pieceCount: Int
    private let imageSize: CGSize
    private let gridRows: Int
    private let gridCols: Int
    private let pieceWidth: CGFloat
    private let pieceHeight: CGFloat

    // Realistic jigsaw configuration
    private let tabRadius: CGFloat = 25 // Base radius for tabs
    private let neckWidth: CGFloat = 15 // Width of tab neck
    private let curveIntensity: CGFloat = 0.8 // How curved the edges are
    private let organicVariation: CGFloat = 0.4 // Random organic variation

    // Store tab directions for consistency between adjacent pieces
    private var horizontalTabs: [[Bool]] = [] // true = right piece has outward tab
    private var verticalTabs: [[Bool]] = [] // true = bottom piece has outward tab

    // MARK: - Initialization
    init(pieceCount: Int, imageSize: CGSize) {
        self.pieceCount = pieceCount
        self.imageSize = imageSize

        // Calculate optimal grid dimensions
        let aspectRatio = imageSize.width / imageSize.height
        let idealCols = sqrt(Double(pieceCount) * Double(aspectRatio))
        let idealRows = sqrt(Double(pieceCount) / Double(aspectRatio))

        self.gridCols = max(2, Int(round(idealCols)))
        self.gridRows = max(2, Int(round(idealRows)))

        self.pieceWidth = imageSize.width / CGFloat(gridCols)
        self.pieceHeight = imageSize.height / CGFloat(gridRows)

        // Initialize tab patterns for consistent interlocking
        self.horizontalTabs = Array(repeating: Array(repeating: false, count: gridCols - 1), count: gridRows)
        self.verticalTabs = Array(repeating: Array(repeating: false, count: gridCols), count: gridRows - 1)

        // Generate random but consistent tab pattern
        generateTabPattern()

        print("🧩 Jigsaw Generator: \(pieceCount) pieces in \(gridRows)x\(gridCols) grid")
        print("📏 Piece size: \(pieceWidth) x \(pieceHeight)")
    }

    // MARK: - Public Methods
    func generatePieces() -> [JigsawPieceInfo] {
        var pieces: [JigsawPieceInfo] = []

        for row in 0..<gridRows {
            for col in 0..<gridCols {
                let pieceID = row * gridCols + col

                // Skip if we've reached the desired piece count
                if pieceID >= pieceCount {
                    break
                }

                let piece = createRealisticPiece(row: row, col: col, pieceID: pieceID)
                pieces.append(piece)
            }
        }

        return pieces
    }

    // MARK: - Private Methods
    private func generateTabPattern() {
        // Generate random tab pattern for horizontal connections
        for row in 0..<gridRows {
            for col in 0..<(gridCols - 1) {
                horizontalTabs[row][col] = Bool.random()
            }
        }

        // Generate random tab pattern for vertical connections
        for row in 0..<(gridRows - 1) {
            for col in 0..<gridCols {
                verticalTabs[row][col] = Bool.random()
            }
        }
    }

    private func createRealisticPiece(row: Int, col: Int, pieceID: Int) -> JigsawPieceInfo {
        let x = CGFloat(col) * pieceWidth
        let y = CGFloat(row) * pieceHeight

        // Determine tab directions based on stored pattern
        let hasTopTab = row > 0 ? !verticalTabs[row - 1][col] : false // Inward if neighbor has outward
        let hasBottomTab = row < gridRows - 1 ? verticalTabs[row][col] : false // Outward
        let hasLeftTab = col > 0 ? !horizontalTabs[row][col - 1] : false // Inward if neighbor has outward
        let hasRightTab = col < gridCols - 1 ? horizontalTabs[row][col] : false // Outward

        // Create realistic jigsaw piece shape
        let shape = createRealisticJigsawShape(
            baseRect: CGRect(x: 0, y: 0, width: pieceWidth, height: pieceHeight),
            hasTopTab: hasTopTab,
            hasRightTab: hasRightTab,
            hasBottomTab: hasBottomTab,
            hasLeftTab: hasLeftTab
        )

        // Image rect for this piece (with some overlap for seamless fitting)
        let overlap: CGFloat = 5
        let imageRect = CGRect(
            x: max(0, x - overlap),
            y: max(0, y - overlap),
            width: min(imageSize.width - max(0, x - overlap), pieceWidth + overlap * 2),
            height: min(imageSize.height - max(0, y - overlap), pieceHeight + overlap * 2)
        )

        return JigsawPieceInfo(
            row: row,
            col: col,
            pieceID: pieceID,
            shape: shape,
            imageRect: imageRect,
            hasTopTab: hasTopTab,
            hasRightTab: hasRightTab,
            hasBottomTab: hasBottomTab,
            hasLeftTab: hasLeftTab
        )
    }

    private func createRealisticJigsawShape(baseRect: CGRect, hasTopTab: Bool, hasRightTab: Bool, hasBottomTab: Bool, hasLeftTab: Bool) -> UIBezierPath {
        let path = UIBezierPath()

        // Add organic variation to the base rectangle
        let margin: CGFloat = 3
        let organicRect = CGRect(
            x: baseRect.minX + CGFloat.random(in: -margin...margin),
            y: baseRect.minY + CGFloat.random(in: -margin...margin),
            width: baseRect.width + CGFloat.random(in: -margin...margin),
            height: baseRect.height + CGFloat.random(in: -margin...margin)
        )

        // Define corner points with organic variation
        let topLeft = CGPoint(x: organicRect.minX, y: organicRect.minY)
        let topRight = CGPoint(x: organicRect.maxX, y: organicRect.minY)
        let bottomRight = CGPoint(x: organicRect.maxX, y: organicRect.maxY)
        let bottomLeft = CGPoint(x: organicRect.minX, y: organicRect.maxY)

        // Start from top-left
        path.move(to: topLeft)

        // Top edge with potential tab
        if hasTopTab {
            addRealisticTab(to: path, from: topLeft, to: topRight, isOutward: true, isHorizontal: true)
        } else {
            addOrganicEdge(to: path, from: topLeft, to: topRight)
        }

        // Right edge with potential tab
        if hasRightTab {
            addRealisticTab(to: path, from: topRight, to: bottomRight, isOutward: true, isHorizontal: false)
        } else {
            addOrganicEdge(to: path, from: topRight, to: bottomRight)
        }

        // Bottom edge with potential tab
        if hasBottomTab {
            addRealisticTab(to: path, from: bottomRight, to: bottomLeft, isOutward: true, isHorizontal: true)
        } else {
            addOrganicEdge(to: path, from: bottomRight, to: bottomLeft)
        }

        // Left edge with potential tab
        if hasLeftTab {
            addRealisticTab(to: path, from: bottomLeft, to: topLeft, isOutward: true, isHorizontal: false)
        } else {
            addOrganicEdge(to: path, from: bottomLeft, to: topLeft)
        }

        path.close()
        return path
    }

    // MARK: - Helper Methods for Realistic Jigsaw Shapes

    private func addOrganicEdge(to path: UIBezierPath, from startPoint: CGPoint, to endPoint: CGPoint) {
        // Create organic, slightly curved edges instead of straight lines
        let distance = sqrt(pow(endPoint.x - startPoint.x, 2) + pow(endPoint.y - startPoint.y, 2))
        let isHorizontal = abs(endPoint.x - startPoint.x) > abs(endPoint.y - startPoint.y)

        // Add multiple control points for more organic curves
        let segments = max(2, Int(distance / 30)) // More segments for longer edges
        var currentPoint = startPoint

        for i in 1...segments {
            let progress = CGFloat(i) / CGFloat(segments)
            let nextPoint = CGPoint(
                x: startPoint.x + (endPoint.x - startPoint.x) * progress,
                y: startPoint.y + (endPoint.y - startPoint.y) * progress
            )

            // Add organic variation
            let variation = organicVariation * distance * 0.01
            let perpOffset = CGFloat.random(in: -variation...variation)

            let controlPoint: CGPoint
            if isHorizontal {
                controlPoint = CGPoint(
                    x: (currentPoint.x + nextPoint.x) / 2,
                    y: (currentPoint.y + nextPoint.y) / 2 + perpOffset
                )
            } else {
                controlPoint = CGPoint(
                    x: (currentPoint.x + nextPoint.x) / 2 + perpOffset,
                    y: (currentPoint.y + nextPoint.y) / 2
                )
            }

            path.addQuadCurve(to: nextPoint, controlPoint: controlPoint)
            currentPoint = nextPoint
        }
    }

    private func addRealisticTab(to path: UIBezierPath, from startPoint: CGPoint, to endPoint: CGPoint, isOutward: Bool, isHorizontal: Bool) {
        let edgeLength = isHorizontal ? abs(endPoint.x - startPoint.x) : abs(endPoint.y - startPoint.y)

        // Tab position with organic variation (not centered)
        let tabPosition = 0.35 + CGFloat.random(in: -0.15...0.15) // 20-50% along edge

        // Calculate tab dimensions with realistic proportions
        let baseRadius = min(tabRadius, edgeLength * 0.35)
        let radiusVariation = CGFloat.random(in: 0.7...1.3) // Asymmetric tabs
        let actualRadius = baseRadius * radiusVariation
        let actualNeckWidth = neckWidth * CGFloat.random(in: 0.8...1.2)

        // Direction multiplier for inward/outward tabs
        let direction: CGFloat = isOutward ? 1 : -1

        // Calculate key points
        let tabCenterPosition = CGPoint(
            x: startPoint.x + (endPoint.x - startPoint.x) * tabPosition,
            y: startPoint.y + (endPoint.y - startPoint.y) * tabPosition
        )

        var tabStartPoint: CGPoint
        var tabEndPoint: CGPoint
        var tabTipPoint: CGPoint

        if isHorizontal {
            tabStartPoint = CGPoint(x: tabCenterPosition.x - actualNeckWidth/2, y: tabCenterPosition.y)
            tabEndPoint = CGPoint(x: tabCenterPosition.x + actualNeckWidth/2, y: tabCenterPosition.y)
            tabTipPoint = CGPoint(x: tabCenterPosition.x, y: tabCenterPosition.y + actualRadius * direction)
        } else {
            tabStartPoint = CGPoint(x: tabCenterPosition.x, y: tabCenterPosition.y - actualNeckWidth/2)
            tabEndPoint = CGPoint(x: tabCenterPosition.x, y: tabCenterPosition.y + actualNeckWidth/2)
            tabTipPoint = CGPoint(x: tabCenterPosition.x + actualRadius * direction, y: tabCenterPosition.y)
        }

        // Draw organic edge to tab start
        addOrganicEdge(to: path, from: startPoint, to: tabStartPoint)

        // Create realistic tab shape with organic curves
        createRealisticTabShape(
            path: path,
            startPoint: tabStartPoint,
            endPoint: tabEndPoint,
            tipPoint: tabTipPoint,
            radius: actualRadius,
            isHorizontal: isHorizontal,
            isOutward: isOutward
        )

        // Draw organic edge from tab end to edge end
        addOrganicEdge(to: path, from: tabEndPoint, to: endPoint)
    }

    private func createRealisticTabShape(path: UIBezierPath, startPoint: CGPoint, endPoint: CGPoint, tipPoint: CGPoint, radius: CGFloat, isHorizontal: Bool, isOutward: Bool) {
        // Create highly realistic jigsaw tab with organic, asymmetric curves

        // Add organic variation to make each tab unique
        let asymmetryX = CGFloat.random(in: 0.7...1.3)
        let asymmetryY = CGFloat.random(in: 0.8...1.2)
        let bulgeVariation = CGFloat.random(in: 0.6...1.4)

        // Calculate intermediate points for organic curves
        let midPoint1 = CGPoint(
            x: (startPoint.x + tipPoint.x) / 2,
            y: (startPoint.y + tipPoint.y) / 2
        )
        let midPoint2 = CGPoint(
            x: (endPoint.x + tipPoint.x) / 2,
            y: (endPoint.y + tipPoint.y) / 2
        )

        if isHorizontal {
            // Horizontal tab with organic bulges
            let bulgeOffset = radius * 0.4 * bulgeVariation

            // First curve: neck to first bulge
            let cp1 = CGPoint(
                x: startPoint.x + (tipPoint.x - startPoint.x) * 0.2,
                y: startPoint.y + (tipPoint.y - startPoint.y) * 0.6 * asymmetryY
            )
            let bulge1 = CGPoint(
                x: midPoint1.x + (isOutward ? bulgeOffset : -bulgeOffset) * asymmetryX,
                y: midPoint1.y
            )
            let cp2 = CGPoint(
                x: tipPoint.x - radius * 0.3,
                y: tipPoint.y + (isOutward ? -radius * 0.2 : radius * 0.2) * asymmetryY
            )

            path.addCurve(to: bulge1, controlPoint1: cp1, controlPoint2: cp2)

            // Second curve: around the tip
            let cp3 = CGPoint(
                x: tipPoint.x,
                y: tipPoint.y + (isOutward ? -radius * 0.4 : radius * 0.4) * asymmetryY
            )
            let cp4 = CGPoint(
                x: tipPoint.x,
                y: tipPoint.y + (isOutward ? radius * 0.4 : -radius * 0.4) * asymmetryY
            )

            path.addCurve(to: CGPoint(x: tipPoint.x + radius * 0.3, y: tipPoint.y), controlPoint1: cp3, controlPoint2: cp4)

            // Third curve: tip to second bulge
            let bulge2 = CGPoint(
                x: midPoint2.x + (isOutward ? bulgeOffset : -bulgeOffset) * asymmetryX,
                y: midPoint2.y
            )
            let cp5 = CGPoint(
                x: tipPoint.x + radius * 0.3,
                y: tipPoint.y + (isOutward ? radius * 0.2 : -radius * 0.2) * asymmetryY
            )
            let cp6 = CGPoint(
                x: endPoint.x - (tipPoint.x - endPoint.x) * 0.2,
                y: endPoint.y + (tipPoint.y - endPoint.y) * 0.6 * asymmetryY
            )

            path.addCurve(to: bulge2, controlPoint1: cp5, controlPoint2: cp6)

            // Final curve: second bulge to neck end
            path.addCurve(to: endPoint, controlPoint1: bulge2, controlPoint2: cp6)

        } else {
            // Vertical tab with organic bulges
            let bulgeOffset = radius * 0.4 * bulgeVariation

            // First curve: neck to first bulge
            let cp1 = CGPoint(
                x: startPoint.x + (tipPoint.x - startPoint.x) * 0.6 * asymmetryX,
                y: startPoint.y + (tipPoint.y - startPoint.y) * 0.2
            )
            let bulge1 = CGPoint(
                x: midPoint1.x,
                y: midPoint1.y + (isOutward ? bulgeOffset : -bulgeOffset) * asymmetryY
            )
            let cp2 = CGPoint(
                x: tipPoint.x + (isOutward ? -radius * 0.2 : radius * 0.2) * asymmetryX,
                y: tipPoint.y - radius * 0.3
            )

            path.addCurve(to: bulge1, controlPoint1: cp1, controlPoint2: cp2)

            // Second curve: around the tip
            let cp3 = CGPoint(
                x: tipPoint.x + (isOutward ? -radius * 0.4 : radius * 0.4) * asymmetryX,
                y: tipPoint.y
            )
            let cp4 = CGPoint(
                x: tipPoint.x + (isOutward ? radius * 0.4 : -radius * 0.4) * asymmetryX,
                y: tipPoint.y
            )

            path.addCurve(to: CGPoint(x: tipPoint.x, y: tipPoint.y + radius * 0.3), controlPoint1: cp3, controlPoint2: cp4)

            // Third curve: tip to second bulge
            let bulge2 = CGPoint(
                x: midPoint2.x,
                y: midPoint2.y + (isOutward ? bulgeOffset : -bulgeOffset) * asymmetryY
            )
            let cp5 = CGPoint(
                x: tipPoint.x + (isOutward ? radius * 0.2 : -radius * 0.2) * asymmetryX,
                y: tipPoint.y + radius * 0.3
            )
            let cp6 = CGPoint(
                x: endPoint.x + (tipPoint.x - endPoint.x) * 0.6 * asymmetryX,
                y: endPoint.y - (tipPoint.y - endPoint.y) * 0.2
            )

            path.addCurve(to: bulge2, controlPoint1: cp5, controlPoint2: cp6)

            // Final curve: second bulge to neck end
            path.addCurve(to: endPoint, controlPoint1: bulge2, controlPoint2: cp6)
        }
    }
}
