//
//  PuzzlePiece.swift
//  PuzzleZone
//
//  Created by <PERSON> on 25.05.2025.
//

import SpriteKit
import UIKit

class PuzzlePiece: SKSpriteNode {

    // MARK: - Properties
    let correctRow: Int
    let correctCol: Int
    let pieceID: Int

    private var originalPosition: CGPoint = .zero
    private var isInCorrectPosition = false
    private let snapDistance: CGFloat = 40
    private var customShape: UIBezierPath?

    // MARK: - Initialization
    init(texture: SKTexture?, row: Int, col: Int, pieceID: Int) {
        self.correctRow = row
        self.correctCol = col
        self.pieceID = pieceID

        super.init(texture: texture, color: .clear, size: texture?.size() ?? CGSize(width: 100, height: 100))

        setupPiece()
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Setup
    private func setupPiece() {
        // Don't enable user interaction on individual pieces - let the scene handle all touches
        isUserInteractionEnabled = false
        name = "puzzlePiece_\(pieceID)"

        // Add simple rectangular border and shadow for the piece
        addSimpleBorder()
    }

    // MARK: - Simple Border
    private func addSimpleBorder() {
        // Remove existing border and shadow
        children.forEach { child in
            if child is SKShapeNode {
                child.removeFromParent()
            }
        }

        // Create simple shadow
        let shadow = SKShapeNode(rectOf: size)
        shadow.fillColor = UIColor.black.withAlphaComponent(0.3)
        shadow.strokeColor = .clear
        shadow.position = CGPoint(x: 2, y: -2)
        shadow.zPosition = -1
        addChild(shadow)

        // Add simple border
        let border = SKShapeNode(rectOf: size)
        border.strokeColor = UIColor.white.withAlphaComponent(0.8)
        border.fillColor = .clear
        border.lineWidth = 2.0
        border.zPosition = 1
        addChild(border)
    }

    // MARK: - Custom Shape (kept for compatibility)
    func setCustomShape(_ shape: UIBezierPath) {
        // For simple rectangular pieces, we just use the simple border
        addSimpleBorder()
    }

    // MARK: - Position Management
    func setOriginalPosition(_ position: CGPoint) {
        originalPosition = position
    }

    func getCorrectPosition(boardPosition: CGPoint, boardSize: CGSize, gridCols: Int, gridRows: Int) -> CGPoint {
        // Calculate position based on the piece's grid position within the board
        let pieceWidth = boardSize.width / CGFloat(gridCols)
        let pieceHeight = boardSize.height / CGFloat(gridRows)

        let offsetX = (CGFloat(correctCol) - CGFloat(gridCols - 1) / 2.0) * pieceWidth
        let offsetY = (CGFloat(gridRows - 1) / 2.0 - CGFloat(correctRow)) * pieceHeight

        return CGPoint(x: boardPosition.x + offsetX, y: boardPosition.y + offsetY)
    }

    func checkIfInCorrectPosition(boardPosition: CGPoint, boardSize: CGSize, gridCols: Int, gridRows: Int) -> Bool {
        let correctPos = getCorrectPosition(boardPosition: boardPosition, boardSize: boardSize, gridCols: gridCols, gridRows: gridRows)
        let distance = sqrt(pow(position.x - correctPos.x, 2) + pow(position.y - correctPos.y, 2))
        return distance < snapDistance
    }

    func snapToCorrectPosition(boardPosition: CGPoint, boardSize: CGSize, gridCols: Int, gridRows: Int, completion: (() -> Void)? = nil) {
        let correctPos = getCorrectPosition(boardPosition: boardPosition, boardSize: boardSize, gridCols: gridCols, gridRows: gridRows)

        let moveAction = SKAction.move(to: correctPos, duration: 0.2)
        moveAction.timingMode = .easeOut

        run(moveAction) { [weak self] in
            self?.isInCorrectPosition = true
            self?.triggerHapticFeedback()
            print("✅ Piece \(self?.pieceID ?? 0) marked as correctly placed!")
            completion?()
        }
    }

    func returnToOriginalPosition() {
        let moveAction = SKAction.move(to: originalPosition, duration: 0.3)
        moveAction.timingMode = .easeOut
        run(moveAction)
        isInCorrectPosition = false
        print("❌ Piece \(pieceID) returned to original position")
    }

    // MARK: - Visual Effects
    func highlight() {
        let scaleUp = SKAction.scale(to: 1.1, duration: 0.1)
        let fadeIn = SKAction.fadeAlpha(to: 1.0, duration: 0.1)
        let group = SKAction.group([scaleUp, fadeIn])
        run(group)
    }

    func unhighlight() {
        let scaleDown = SKAction.scale(to: 1.0, duration: 0.1)
        let fadeOut = SKAction.fadeAlpha(to: 0.9, duration: 0.1)
        let group = SKAction.group([scaleDown, fadeOut])
        run(group)
    }

    // MARK: - Haptic Feedback
    private func triggerHapticFeedback() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    // MARK: - Status
    var isCorrectlyPlaced: Bool {
        return isInCorrectPosition
    }
}
